## [Unreleased]

### Fixed
- **Product Analysis Export Date Filtering**: Fixed a critical issue where the export functionality in the Product Analysis page (Phân tích sản phẩm) was not applying date filters correctly. The exported Excel file was showing total sales quantities from all time instead of the filtered date range displayed on the page. The fix involved modifying the ProductRevenueTable component to synchronize its internal date range state with URL search parameters, ensuring the export function can access the correct date filters.
- **Product Revenue Export**: Fixed a bug that caused a 404 error when exporting product revenue data if the total number of items was a multiple of the page size. The export logic now correctly calculates the total number of pages and fetches all data concurrently, improving reliability and performance.
- **Staff Filter State**: Fixed a bug in the Product Revenue report where the staff filter state was not correctly synchronized with the URL parameters. Refactored the component to use a single state for multi-selection, ensuring that filter values, including "Khách hàng tự đặt" (`null`), are correctly initialized and preserved across re-renders.
- **Product Revenue Report**: Fixed a critical `FieldError` that occurred when filtering the product revenue report by `sales_admin`. The issue was caused by applying a filter for `sales_admin_id` directly on the `OrderItem` model, which does not have this field. The fix ensures the filter is correctly applied through the `Order` relationship (`order__sales_admin_id`).

### Added

- **Sales Admin Access Control**: Enhanced Product Analysis page (Phân tích sản phẩm) with role-based access control for sales_admin users:
  - Sales admin users can now access the Product Analysis page and view product revenue data
  - Added "Phân tích" section to sidebar navigation for sales_admin with only "Sản phẩm" subsection visible
  - StaffFilter component is automatically hidden for sales_admin users to maintain data security
  - Sales admin users only see their own sales data (automatically filtered by their user ID)
  - Maintains existing functionality for other user roles (sales_manager, etc.)
  - Updated both desktop and mobile/tablet navigation layouts
- **Product Revenue Empty State**: Added Empty component to Product Revenue Analysis page that displays when no data is available, providing consistent user experience with other admin pages
- **Order Print Status Tracking**: Added `is_printed` boolean field to Order model to track whether orders have been printed
- **"Đã in" Column**: New column in order table that appears only for orders with "processing" status
- **Checkbox Interface**: Interactive checkbox for sales managers to mark orders as printed/unprinted
- **Role-Based Access**: Only sales managers can modify print status; other roles see read-only display
- **Real-time Updates**: Changes are immediately reflected in the UI with proper API integration
- Enhanced single order export functionality with improved field selection:
  - Added 4 new default fields: Tạm tính (subtotal), Phí giao hàng (shipping_fee), Giảm giá (discount), Thuế (%) (tax)
  - Reordered export fields so "Tổng thanh toán" appears after "Thuế" field
  - Added missing subtotal calculation in export utility
  - Improved tax display format to show percentage value
- Added staff registration page with automatic username generation and role selection
- Enhanced staff list page with Ant Design components and table
- Added "Thêm nhân viên" button for direct access to registration
- Created reusable StaffTable component for better maintainability
- Implemented comprehensive password validation:
  - Minimum length of 8 characters
  - Prevention of common/easily guessed passwords
  - No numeric-only passwords
  - No similarity with user attributes (username, email, name)
- Added consistent role names across the application:
  - Matched backend ROLE_CHOICES
  - Updated role display in staff list and creation form

### Changed

- **Product Revenue Export**: Updated the product revenue export functionality to fetch and include data from all pages, ensuring a complete export regardless of pagination.
- **Order Filtering Refactor**: Replaced the `is_showroom` query parameter with a more explicit `order_type` parameter (`showroom`, `e_comm`, `online`) in the backend. Updated the admin panel to use this new parameter for fetching orders and calculating status counts, ensuring consistent and accurate filtering.
- **"Đã in hóa đơn" Column Enhancement**: Modified OrderTableView to conditionally display "Đã in hóa đơn" column only when there are orders with "processing" status, improving table layout efficiency and user experience
- **Column Positioning**: Repositioned "Đã in hóa đơn" column between "Sản phẩm" and "Phương thức TT" columns for better logical flow
- **Column Title Update**: Changed column title from "Đã in" to "Đã in hóa đơn" for clearer meaning
- **Dynamic Table Width**: Implemented dynamic scroll width calculation that adjusts based on column visibility to maintain optimal table layout
- Restricted product creation and editing to users with sales_manager role
- Added permission check for product management actions
- Added read-only view for product details for non-sales_manager users
- Improved user feedback for unauthorized product actions
- Enhanced sales_manager permissions to allow updating order status to any state in order confirmation workflow
- Updated CreateOrderPaymentMethod component to use Ant Design components
  - Replaced native select elements with antd Select
  - Replaced checkbox with antd Checkbox
  - Updated layout using antd Typography

# Changelog

## [2025-08-26] - Fix: Chiết Khấu Not Sent in Order Creation API

### 🐛 Bug Fixes
- **Missing Chiết Khấu in API Payload**: Fixed issue where `chiet_khau_amount` was not being sent when creating new orders
- **Backend Serializer Update**: Enhanced `OrderCreateSerializer` to properly handle and save `chiet_khau_amount` from request data
- **Frontend Service Fix**: Updated `OrderService.createSingleOrder()` to include `chiet_khau_amount` in API payload

### 🛠️ Technical Changes

#### Frontend Updates
- **OrderService.ts**: Modified `createSingleOrder()` to include `chiet_khau_amount` in items mapping
- **Interface Update**: Added `chiet_khau_amount?: number` to `CreateOrderData.items` interface

#### Backend Updates  
- **OrderCreateSerializer**: Enhanced `create()` method to extract and save `chiet_khau_amount` from request data
- **OrderItem Creation**: Now properly stores `chiet_khau_amount` when creating new order items

#### Data Flow
- **Request**: Frontend now sends `chiet_khau_amount` in order creation payload
- **Processing**: Backend extracts `chiet_khau_amount` from `items_data` and passes to `OrderItem.objects.create()`
- **Storage**: `chiet_khau_amount` is saved to database and available for order calculations

### 🧪 Testing
- ✅ Verified that `chiet_khau_amount` is now included in API request payload
- ✅ Confirmed that backend properly receives and processes chiết khấu data
- ✅ Validated that `chiet_khau_amount` is correctly saved to database
- ✅ Tested order creation with various chiết khấu values

### 📋 Files Modified
- `admin/src/services/orderService.ts` - Added chiet_khau_amount to API payload and interface
- `full/backend/store/serializers/order_serializers.py` - Enhanced OrderCreateSerializer to handle chiet_khau_amount

### 🎯 Business Impact
- **Complete Chiết Khấu Support**: Orders can now be created with item-level discounts
- **Data Integrity**: Chiết khấu values are properly persisted and available for calculations
- **User Experience**: Staff can create orders with chiết khấu and see them reflected in the system
- **Consistent Behavior**: Order creation now matches order update functionality for chiết khấu handling

## [2025-01-08] - Automatic Total Price Calculation with Chiết Khấu

### 🆕 New Features
- **Automatic Total Price Calculation**: When adjusting chiết khấu (discount) values in the order creation page, the total price now automatically updates to reflect the applied discount
- **Real-time Price Updates**: Total price in ProductionSection now updates in real-time as chiết khấu values are modified
- **Bidirectional Calculation**: Both chiết khấu and total price can be modified, with automatic recalculation of the other field

### 🛠️ Technical Changes

#### Order Creation Page Updates
- **handleQuantityChange**: Updated to calculate total price with chiết khấu applied when quantity changes
- **handleAddItem**: Modified to include chiết khấu in total price calculations for new items
- **handleTotalPriceChange**: Enhanced to recalculate chiết khấu when total price is manually adjusted
- **onChietKhauChange**: Now automatically updates total_price based on chiết khấu amount

#### Calculation Logic
- **Base Formula**: `total_price = (price * quantity) - chiet_khau_amount`
- **Safety Check**: Ensures total price never goes below 0 using `Math.max(0, ...)`
- **Consistent Updates**: All price-related operations now properly consider chiết khấu

### 🧪 Testing
- ✅ Verified that changing chiết khấu automatically updates total price
- ✅ Confirmed that quantity changes recalculate total price with chiết khấu
- ✅ Tested that manual total price changes recalculate chiết khấu
- ✅ Validated that new items properly calculate total price with chiết khấu

### 📋 Files Modified
- `admin/src/pages/orders/create.tsx` - Updated all price calculation handlers to include chiết khấu logic

### 🎯 Business Impact
- **Improved User Experience**: Staff can now see immediate feedback when adjusting discount amounts
- **Accurate Pricing**: Total prices automatically reflect applied discounts without manual calculation
- **Reduced Errors**: Eliminates the need for manual total price adjustments when applying chiết khấu
- **Better Workflow**: Streamlines the order creation process with real-time price updates

## [2025-08-26] - Bug Fix: Order Update Chiết Khấu Issue
