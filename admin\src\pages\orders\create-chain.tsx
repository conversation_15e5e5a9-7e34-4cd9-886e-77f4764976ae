import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, Button } from "antd";

import { slugify } from "@/lib/utils";
import { api, endpoints } from "@/lib/api";
import { useAuth } from "@/context/auth-hooks";
import { useToast } from "@/context/toast-hooks";

import { Customer } from "@/types";
import { isApiError } from "@/types/api";
import ChainProductTable from "@/components/orders/ChainProductTable";
import { CustomerSearchDialog } from "@/components/customers";
import { ShippingAddressForm, ChainCustomerInfoForm, type ChainOrderForm } from "./components";
import { formatErrorMessage } from "./utils";

interface ChainOrderItem {
  id: number;
  name: string;
  code?: string;
  price: number;
  quantity: number;
  total_price: number;
  unit?: string;
  weight?: number;
}


export default function CreateChainOrderPage() {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { user } = useAuth();

  const [chainProducts, setChainProducts] = useState<ChainOrderItem[]>([]);
  const [loading, setLoading] = useState(false);

  // Customer-related states
  const [useCustomerInfo, setUseCustomerInfo] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState(false);
  const [isCreatingNewCustomer, setIsCreatingNewCustomer] = useState(false);

  const [newCustomerForm, setNewCustomerForm] = useState({
    first_name: "",
    last_name: "",
    phone_number: "",
    shipping_address: "",
    ward: "",
    district: "",
    city: "",
    creator_id: user?.id || 0,
  });

  const [orderForm, setOrderForm] = useState<ChainOrderForm>({
    user: user?.id,
    phone_number: user?.phone_number || "",
    email: user?.email || "",
    shipping_address: "",
    ward: "",
    district: "",
    city: "",
    payment_method: "cash" as const, // Mặc định là Tiền mặt
    payment_status: "unpaid" as const, // Mặc định là chưa thanh toán
    company_payment_received: false,
    shipping_fee: 0,
    discount: 0,
    is_chain: true,
    tax: 0,
    have_tax: false,
  });





  const handleChainProductsChange = (products: any[]) => {
    const formattedProducts: ChainOrderItem[] = products.map(product => ({
      id: product.id,
      name: product.name,
      code: product.code,
      price: product.chain_price || 0,
      quantity: product.quantity,
      total_price: product.total_price,
      unit: product.unit,
      weight: product.weight,
    }));
    setChainProducts(formattedProducts);
  };

  const handleOrderFormChange = (field: string, value: string) => {
    setOrderForm(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Customer management handlers
  const handleSelectCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setOrderForm((prev) => ({
      ...prev,
      user: customer.id,
      phone_number: customer.profile.phone_number || "",
      email: customer.email,
      shipping_address: customer.profile.shipping_address || "",
      ward: customer.profile.ward || "",
      district: customer.profile.district || "",
      city: customer.profile.city || "",
    }));
    setIsCustomerDialogOpen(false);
  };

  const handleRemoveCustomer = () => {
    setSelectedCustomer(null);
    setOrderForm((prev) => ({
      ...prev,
      user: user?.id,
      phone_number: user?.phone_number || "",
      email: user?.email || "",
      shipping_address: "",
      ward: "",
      district: "",
      city: "",
    }));
  };

  const handleUseCustomerInfoChange = (use: boolean) => {
    setUseCustomerInfo(use);
    if (!use) {
      handleRemoveCustomer();
      setIsCreatingNewCustomer(false);
    }
  };

  const createCustomer = async () => {
    // Validate required fields
    if (!newCustomerForm.first_name) {
      throw new Error("Vui lòng nhập tên khách hàng");
    }
    if (!newCustomerForm.phone_number) {
      throw new Error("Vui lòng nhập số điện thoại");
    }
    if (!newCustomerForm.shipping_address) {
      throw new Error("Vui lòng nhập địa chỉ giao hàng");
    }
    if (
      !newCustomerForm.ward ||
      !newCustomerForm.district ||
      !newCustomerForm.city
    ) {
      throw new Error("Vui lòng nhập đầy đủ thông tin phường, quận, thành phố");
    }

    const timestamp = Date.now().toString().slice(-6);
    const email = `${slugify(
      newCustomerForm.first_name
    )}-${timestamp}@placeholder.com`;

    // Create user with the form data
    const response = await api.post(endpoints.customers.createCustomer, {
      ...newCustomerForm,
      last_name: "",
      email,
    });

    const newUser = response.data.user;

    const orderFormData = {
      user: newUser.id,
      phone_number: newCustomerForm.phone_number,
      email: newUser.email,
      shipping_address: newCustomerForm.shipping_address,
      ward: newCustomerForm.ward,
      district: newCustomerForm.district,
      city: newCustomerForm.city,
      payment_method: orderForm.payment_method,
      payment_status: orderForm.payment_status,
      company_payment_received: orderForm.company_payment_received,
      shipping_fee: orderForm.shipping_fee,
      discount: orderForm.discount,
      is_chain: true,
      tax: orderForm.tax,
      have_tax: orderForm.have_tax,
    };

    const newSelectedCustomer = {
      id: newUser.id,
      username: newUser.username,
      first_name: newUser.first_name,
      last_name: newUser.last_name,
      email: newUser.email,
      phone_number: newUser.phone_number,
      is_active: newUser.is_active,
      rank: "normal" as const,
      profile: {
        phone_number: newCustomerForm.phone_number,
        shipping_address: newCustomerForm.shipping_address,
        ward: newCustomerForm.ward,
        district: newCustomerForm.district,
        city: newCustomerForm.city,
        rank: "normal" as const,
      },
    };

    setSelectedCustomer(newSelectedCustomer);
    setOrderForm(orderFormData);
    setIsCreatingNewCustomer(false);

    return { user: newUser, orderForm: orderFormData };
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      // Validate required fields
      if (chainProducts.length === 0) {
        showToast("Vui lòng chọn ít nhất một sản phẩm", "error");
        return;
      }

      if (!user) {
        showToast("Không thể xác định người dùng", "error");
        return;
      }

      // Handle customer creation if needed
      if (useCustomerInfo && isCreatingNewCustomer) {
        try {
          await createCustomer();
        } catch (error) {
          console.error("Không thể tạo khách hàng:", error);
          if (error instanceof Error) {
            showToast(error.message, "error");
          } else {
            showToast("Không thể tạo khách hàng", "error");
          }
          return;
        }
      }

      if (!orderForm.shipping_address.trim()) {
        showToast("Vui lòng nhập địa chỉ giao hàng", "error");
        return;
      }

      if (!orderForm.city || !orderForm.district || !orderForm.ward) {
        showToast("Vui lòng chọn đầy đủ thông tin thành phố, quận/huyện, phường/xã", "error");
        return;
      }

      // Prepare order data
      const orderData = {
        ...orderForm,
        sales_admin: user.id, // Gán sales_admin là user đang đăng nhập
        items: chainProducts.map(product => ({
          product: product.id,
          quantity: product.quantity,
          price: product.price, // Đây là price đã được format từ chain_price ở trên
          total_price: product.total_price,
        })),
      };

      // Create order
      const response = await api.post(endpoints.orders.create, orderData);

      showToast("Đơn hàng chuỗi đã được tạo thành công!", "success");
      navigate(`/orders/${response.data.id}`);
    } catch (error) {
      console.error("Lỗi khi tạo đơn hàng:", error);

      if (isApiError(error)) {
        const errorMessage = error.response.data[0] || "Lỗi không xác định";
        const formattedMessage = formatErrorMessage(errorMessage);
        showToast(`Không thể tạo đơn hàng chuỗi:\n${formattedMessage}`, "error");
      } else {
        showToast("Không thể tạo đơn hàng chuỗi. Vui lòng thử lại", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Tạo đơn hàng chuỗi</h1>
        <Button onClick={() => navigate("/orders/chain")}>
          Quay lại danh sách
        </Button>
      </div>

      {/* Chain Products Table */}
      <Card title="Sản phẩm chuỗi" className="mt-6">
        <ChainProductTable onProductsChange={handleChainProductsChange} />
      </Card>

      {/* Customer Information Form */}
      <ChainCustomerInfoForm
        selectedCustomer={selectedCustomer}
        isCreatingNewCustomer={isCreatingNewCustomer}
        useCustomerInfo={useCustomerInfo}
        newCustomerForm={newCustomerForm}
        orderForm={orderForm}
        onNewCustomerFormChange={(field: any, value: any) => {
          setNewCustomerForm({
            ...newCustomerForm,
            [field]: value,
          });
        }}
        onOrderFormChange={handleOrderFormChange}
        onSelectCustomerClick={() => setIsCustomerDialogOpen(true)}
        onRemoveCustomer={handleRemoveCustomer}
        onCancelCreateNewCustomer={() => setIsCreatingNewCustomer(false)}
        onUseCustomerInfoChange={handleUseCustomerInfoChange}
      />

      {/* Shipping Address Form - Only show when not using customer info */}
      {!useCustomerInfo && (
        <ShippingAddressForm
          orderForm={orderForm}
          onOrderFormChange={handleOrderFormChange}
        />
      )}

      {/* Create Order Button */}
      <div className="mt-8 flex justify-end">
        <Button
          type="primary"
          size="large"
          onClick={handleSubmit}
          loading={loading}
          disabled={
            loading ||
            chainProducts.length === 0
          }
          className="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600"
        >
          {loading ? "Đang tạo..." : "Tạo đơn hàng chuỗi"}
        </Button>
      </div>

      {/* Customer Search Dialog */}
      <CustomerSearchDialog
        isOpen={isCustomerDialogOpen}
        onClose={() => setIsCustomerDialogOpen(false)}
        onSelect={handleSelectCustomer}
        onCreateNew={() => {
          setIsCustomerDialogOpen(false);
          setIsCreatingNewCustomer(true);
        }}
      />
    </div>
  );
}
